"""Coordinator agent for orchestrating multi-agent research workflow."""

from typing import List, Dict, Any, Optional, Literal
from langchain_core.messages import HumanMessage, AIMessage
from .base_agent import BaseAgent, AgentState


class CoordinatorAgent(BaseAgent):
    """Agent responsible for coordinating the research workflow and making decisions."""
    
    def __init__(self):
        super().__init__(
            name="CoordinatorAgent",
            description="Orchestrates the multi-agent research workflow and makes strategic decisions"
        )
        self.workflow_state = "initialized"
        self.iteration_count = 0
        self.max_iterations = 3
    
    async def process(self, state: AgentState) -> AgentState:
        """Coordinate the research workflow."""
        # Determine next action based on current state
        next_action = await self._determine_next_action(state)
        
        # Update workflow state
        state = self.add_to_context(state, "next_action", next_action)
        state = self.add_to_context(state, "workflow_state", self.workflow_state)
        state = self.add_to_context(state, "iteration_count", self.iteration_count)
        
        # Create coordination message
        coordination_message = await self._create_coordination_message(state, next_action)
        state.messages.append(self.create_message(coordination_message))
        
        # Log coordination action
        state = self.log_action(state, "workflow_coordination", {
            "next_action": next_action,
            "workflow_state": self.workflow_state,
            "iteration": self.iteration_count
        })
        
        return state
    
    async def _determine_next_action(self, state: AgentState) -> str:
        """Determine the next action in the workflow."""
        search_results = self.get_from_context(state, "search_results", [])
        analysis_results = self.get_from_context(state, "analysis_results", {})
        current_task = state.current_task
        
        # Build context for decision making
        context_info = f"""
        Current Task: {current_task}
        Search Results Available: {len(search_results)} sources
        Analysis Completed: {'Yes' if analysis_results else 'No'}
        Current Iteration: {self.iteration_count}
        Max Iterations: {self.max_iterations}
        Workflow State: {self.workflow_state}
        """
        
        prompt = f"""
        As a research coordinator, determine the next best action based on the current state:
        
        {context_info}
        
        Available actions:
        1. "search" - Conduct initial or additional web search
        2. "analyze" - Analyze existing search results
        3. "refine_search" - Refine search based on analysis gaps
        4. "deep_analysis" - Perform deeper analysis on specific aspects
        5. "generate_report" - Generate final research report
        6. "complete" - Mark research as complete
        
        Consider:
        - Whether we have sufficient information
        - Quality and coverage of current data
        - Information gaps identified
        - Whether we've reached iteration limits
        
        Return only the action name.
        """
        
        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        action = response.content.strip().lower()
        
        # Validate and update workflow state
        valid_actions = ["search", "analyze", "refine_search", "deep_analysis", "generate_report", "complete"]
        if action not in valid_actions:
            action = self._fallback_action(state)
        
        self._update_workflow_state(action)
        return action
    
    def _fallback_action(self, state: AgentState) -> str:
        """Determine fallback action when LLM response is invalid."""
        search_results = self.get_from_context(state, "search_results", [])
        analysis_results = self.get_from_context(state, "analysis_results", {})
        
        if not search_results:
            return "search"
        elif not analysis_results:
            return "analyze"
        elif self.iteration_count >= self.max_iterations:
            return "generate_report"
        else:
            return "refine_search"
    
    def _update_workflow_state(self, action: str):
        """Update the workflow state based on the next action."""
        state_transitions = {
            "search": "searching",
            "analyze": "analyzing", 
            "refine_search": "refining",
            "deep_analysis": "deep_analyzing",
            "generate_report": "reporting",
            "complete": "completed"
        }
        
        self.workflow_state = state_transitions.get(action, self.workflow_state)
        
        if action in ["search", "refine_search"]:
            self.iteration_count += 1
    
    async def _create_coordination_message(self, state: AgentState, next_action: str) -> str:
        """Create a coordination message explaining the next action."""
        action_descriptions = {
            "search": "🔍 Initiating web search to gather information",
            "analyze": "📊 Analyzing collected search results",
            "refine_search": "🎯 Refining search based on identified gaps",
            "deep_analysis": "🔬 Conducting deeper analysis on specific aspects",
            "generate_report": "📝 Generating comprehensive research report",
            "complete": "✅ Research workflow completed"
        }
        
        base_message = f"🎯 **Coordination Decision**\n\n"
        base_message += f"**Next Action:** {action_descriptions.get(next_action, next_action)}\n"
        base_message += f"**Iteration:** {self.iteration_count}/{self.max_iterations}\n"
        base_message += f"**Workflow State:** {self.workflow_state}\n\n"
        
        # Add context-specific information
        if next_action == "search":
            base_message += "Preparing to search for relevant information on the research topic."
        elif next_action == "analyze":
            search_count = len(self.get_from_context(state, "search_results", []))
            base_message += f"Ready to analyze {search_count} search results for insights and patterns."
        elif next_action == "refine_search":
            gaps = self.get_from_context(state, "analysis_results", {}).get("information_gaps", [])
            base_message += f"Identified {len(gaps)} information gaps that need additional research."
        elif next_action == "generate_report":
            base_message += "Sufficient information gathered. Proceeding to generate final report."
        elif next_action == "complete":
            base_message += "Research objectives achieved. Workflow completed successfully."
        
        return base_message
    
    async def evaluate_research_quality(self, state: AgentState) -> Dict[str, Any]:
        """Evaluate the quality and completeness of the research."""
        search_results = self.get_from_context(state, "search_results", [])
        analysis_results = self.get_from_context(state, "analysis_results", {})
        
        # Prepare evaluation context
        context = f"""
        Research Task: {state.current_task}
        Sources Found: {len(search_results)}
        Analysis Completed: {'Yes' if analysis_results else 'No'}
        Key Insights: {len(analysis_results.get('key_insights', []))}
        Themes Identified: {len(analysis_results.get('themes', []))}
        Information Gaps: {len(analysis_results.get('information_gaps', []))}
        """
        
        prompt = f"""
        Evaluate the quality and completeness of this research:
        
        {context}
        
        Provide scores (1-10) for:
        1. Information Coverage - How well does the research cover the topic?
        2. Source Quality - How reliable and credible are the sources?
        3. Analysis Depth - How thorough is the analysis?
        4. Completeness - How complete is the research for the given task?
        
        Also provide:
        - Overall assessment
        - Strengths
        - Areas for improvement
        - Recommendation (continue research, generate report, or complete)
        
        Format as structured text.
        """
        
        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        
        # Parse evaluation (simplified parsing)
        evaluation = {
            "overall_score": 7.0,  # Default score
            "coverage_score": 7.0,
            "quality_score": 7.0,
            "depth_score": 7.0,
            "completeness_score": 7.0,
            "assessment": response.content,
            "recommendation": "continue"
        }
        
        # Store evaluation
        state = self.add_to_context(state, "research_evaluation", evaluation)
        
        return evaluation
    
    async def should_continue_research(self, state: AgentState) -> bool:
        """Determine if research should continue or if it's sufficient."""
        if self.iteration_count >= self.max_iterations:
            return False
        
        evaluation = await self.evaluate_research_quality(state)
        
        # Continue if overall quality is below threshold or if there are significant gaps
        quality_threshold = 7.0
        gaps = self.get_from_context(state, "analysis_results", {}).get("information_gaps", [])
        
        should_continue = (
            evaluation["overall_score"] < quality_threshold or
            len(gaps) > 2 or
            len(self.get_from_context(state, "search_results", [])) < 5
        )
        
        return should_continue
    
    def get_workflow_status(self) -> Dict[str, Any]:
        """Get current workflow status."""
        return {
            "state": self.workflow_state,
            "iteration": self.iteration_count,
            "max_iterations": self.max_iterations,
            "progress": min(self.iteration_count / self.max_iterations, 1.0)
        }
