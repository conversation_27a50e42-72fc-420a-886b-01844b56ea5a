"""Configuration management for the multi-agent research application."""

import os
from typing import Optional
from dotenv import load_dotenv
from pydantic import BaseModel, Field

# Load environment variables
load_dotenv()


class Config(BaseModel):
    """Application configuration."""
    
    # API Keys
    openai_api_key: Optional[str] = Field(default_factory=lambda: os.getenv("OPENAI_API_KEY"))
    anthropic_api_key: Optional[str] = Field(default_factory=lambda: os.getenv("ANTHROPIC_API_KEY"))
    tavily_api_key: Optional[str] = Field(default_factory=lambda: os.getenv("TAVILY_API_KEY"))
    
    # Model Configuration
    default_llm_provider: str = Field(default_factory=lambda: os.getenv("DEFAULT_LLM_PROVIDER", "openai"))
    default_model: str = Field(default_factory=lambda: os.getenv("DEFAULT_MODEL", "gpt-4o"))
    anthropic_model: str = Field(default_factory=lambda: os.getenv("ANTHROPIC_MODEL", "claude-3-5-sonnet-20241022"))
    
    # Application Settings
    max_search_results: int = Field(default_factory=lambda: int(os.getenv("MAX_SEARCH_RESULTS", "10")))
    max_research_iterations: int = Field(default_factory=lambda: int(os.getenv("MAX_RESEARCH_ITERATIONS", "3")))
    streamlit_port: int = Field(default_factory=lambda: int(os.getenv("STREAMLIT_PORT", "8501")))
    debug: bool = Field(default_factory=lambda: os.getenv("DEBUG", "false").lower() == "true")
    
    def validate_api_keys(self) -> bool:
        """Validate that required API keys are present."""
        required_keys = []
        
        if not self.tavily_api_key:
            required_keys.append("TAVILY_API_KEY")
            
        if self.default_llm_provider == "openai" and not self.openai_api_key:
            required_keys.append("OPENAI_API_KEY")
        elif self.default_llm_provider == "anthropic" and not self.anthropic_api_key:
            required_keys.append("ANTHROPIC_API_KEY")
            
        if required_keys:
            raise ValueError(f"Missing required API keys: {', '.join(required_keys)}")
            
        return True


# Global configuration instance
config = Config()
