"""Research history page for the multi-agent research application."""

import streamlit as st
import json
from datetime import datetime
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))


def main():
    """Main history page."""
    st.set_page_config(
        page_title="History - Multi-Agent Assistant",
        page_icon="📚",
        layout="wide"
    )
    
    st.title("📚 Research History")
    st.markdown("*View and manage your previous research sessions*")
    
    # Initialize session state for history
    if "research_history" not in st.session_state:
        st.session_state.research_history = []
    
    if "selected_research" not in st.session_state:
        st.session_state.selected_research = None
    
    # Sidebar for history management
    with st.sidebar:
        st.header("🛠️ History Management")
        
        # History statistics
        total_research = len(st.session_state.research_history)
        st.metric("Total Research Sessions", total_research)
        
        if total_research > 0:
            # Calculate some basic stats
            total_sources = sum(len(r.get('search_results', [])) for r in st.session_state.research_history)
            avg_sources = total_sources / total_research if total_research > 0 else 0
            
            st.metric("Total Sources Found", total_sources)
            st.metric("Avg Sources per Research", f"{avg_sources:.1f}")
        
        st.divider()
        
        # History actions
        st.subheader("Actions")
        
        if st.button("🗑️ Clear All History", type="secondary"):
            if st.session_state.research_history:
                st.session_state.research_history = []
                st.session_state.selected_research = None
                st.success("History cleared!")
                st.rerun()
        
        if st.button("📥 Export History", type="secondary"):
            if st.session_state.research_history:
                export_history()
        
        # File uploader for importing history
        uploaded_file = st.file_uploader("📤 Import History", type=['json'])
        if uploaded_file is not None:
            import_history(uploaded_file)
    
    # Main content area
    if not st.session_state.research_history:
        # Empty state
        st.info("🔍 No research history yet. Start your first research session!")
        
        col1, col2, col3 = st.columns([1, 1, 1])
        with col2:
            if st.button("🚀 Start Research", type="primary", use_container_width=True):
                st.switch_page("pages/1_🔍_Research.py")
    
    else:
        # Display history
        display_history_interface()


def display_history_interface():
    """Display the main history interface."""
    # History overview
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.subheader("📋 Research Sessions")
        
        # Search/filter history
        search_term = st.text_input("🔍 Search history", placeholder="Search by topic or keyword...")
        
        # Filter options
        with st.expander("🔧 Filter Options"):
            date_range = st.date_input("Date Range", value=[], help="Filter by date range")
            min_sources = st.slider("Minimum Sources", 0, 50, 0, help="Filter by minimum number of sources")
        
        # Display history list
        filtered_history = filter_history(search_term, date_range, min_sources)
        
        if not filtered_history:
            st.info("No research sessions match your filters.")
        else:
            for i, research in enumerate(filtered_history):
                display_history_item(research, i)
    
    with col2:
        st.subheader("📄 Research Details")
        
        if st.session_state.selected_research:
            display_research_details(st.session_state.selected_research)
        else:
            st.info("👈 Select a research session from the list to view details")


def filter_history(search_term, date_range, min_sources):
    """Filter history based on search criteria."""
    filtered = st.session_state.research_history.copy()
    
    # Text search
    if search_term:
        filtered = [r for r in filtered if search_term.lower() in r.get('task', '').lower()]
    
    # Source count filter
    if min_sources > 0:
        filtered = [r for r in filtered if len(r.get('search_results', [])) >= min_sources]
    
    # Date range filter (if implemented with timestamps)
    # This would require adding timestamps to research results
    
    return filtered


def display_history_item(research, index):
    """Display a single history item."""
    task = research.get('task', 'Unknown Task')
    search_results = research.get('search_results', [])
    analysis = research.get('analysis_results', {})
    
    # Create a card-like display
    with st.container():
        # Header with task name
        if st.button(f"📄 {task[:50]}{'...' if len(task) > 50 else ''}", 
                    key=f"history_item_{index}",
                    use_container_width=True):
            st.session_state.selected_research = research
            st.rerun()
        
        # Quick stats
        col1, col2, col3 = st.columns(3)
        with col1:
            st.caption(f"📊 {len(search_results)} sources")
        with col2:
            st.caption(f"💡 {len(analysis.get('key_insights', []))} insights")
        with col3:
            st.caption(f"🎯 {len(analysis.get('themes', []))} themes")
        
        st.divider()


def display_research_details(research):
    """Display detailed view of selected research."""
    task = research.get('task', 'Unknown Task')
    search_results = research.get('search_results', [])
    analysis = research.get('analysis_results', {})
    final_report = research.get('final_report', {})
    
    # Header
    st.markdown(f"### 🎯 {task}")
    
    # Quick actions
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🔄 Re-run Research", use_container_width=True):
            # Set up for re-running the research
            st.session_state.rerun_task = task
            st.switch_page("pages/1_🔍_Research.py")
    
    with col2:
        if st.button("📋 Copy Task", use_container_width=True):
            st.session_state.copied_task = task
            st.success("Task copied! Go to Research page to paste.")
    
    with col3:
        if final_report.get('full_report'):
            st.download_button(
                "📥 Download Report",
                data=final_report['full_report'],
                file_name=f"report_{task.replace(' ', '_')[:30]}.md",
                mime="text/markdown",
                use_container_width=True
            )
    
    st.divider()
    
    # Tabbed detail view
    tab1, tab2, tab3, tab4 = st.tabs(["📊 Overview", "💡 Insights", "📚 Sources", "📄 Report"])
    
    with tab1:
        display_overview_tab(research)
    
    with tab2:
        display_insights_tab(analysis)
    
    with tab3:
        display_sources_tab(search_results, analysis)
    
    with tab4:
        display_report_tab(final_report)


def display_overview_tab(research):
    """Display overview tab for research details."""
    search_results = research.get('search_results', [])
    analysis = research.get('analysis_results', {})
    
    # Key metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Sources Found", len(search_results))
    
    with col2:
        st.metric("Key Insights", len(analysis.get('key_insights', [])))
    
    with col3:
        st.metric("Themes Identified", len(analysis.get('themes', [])))
    
    with col4:
        credibility = analysis.get('credibility_analysis', {})
        avg_cred = credibility.get('average_credibility', 0)
        st.metric("Avg Source Quality", f"{avg_cred:.0%}")
    
    # Research summary
    st.subheader("📋 Research Summary")
    
    insights = analysis.get('key_insights', [])
    if insights:
        st.write("**Top Insights:**")
        for i, insight in enumerate(insights[:3], 1):
            st.write(f"{i}. {insight}")
    
    themes = analysis.get('themes', [])
    if themes:
        st.write("**Major Themes:**")
        for theme in themes[:3]:
            st.write(f"• **{theme.get('name', 'Unknown')}**: {theme.get('description', 'No description')}")
    
    # Information gaps
    gaps = analysis.get('information_gaps', [])
    if gaps:
        st.write("**Information Gaps Identified:**")
        for gap in gaps[:3]:
            st.write(f"• {gap}")


def display_insights_tab(analysis):
    """Display insights tab for research details."""
    insights = analysis.get('key_insights', [])
    themes = analysis.get('themes', [])
    
    if insights:
        st.subheader("🔍 Key Insights")
        for i, insight in enumerate(insights, 1):
            st.write(f"**{i}.** {insight}")
        st.divider()
    
    if themes:
        st.subheader("🎯 Thematic Analysis")
        for theme in themes:
            with st.expander(f"📌 {theme.get('name', 'Unknown Theme')}"):
                st.write(f"**Description:** {theme.get('description', 'No description')}")
                
                points = theme.get('points', [])
                if points:
                    st.write("**Supporting Points:**")
                    for point in points:
                        st.write(f"• {point}")


def display_sources_tab(search_results, analysis):
    """Display sources tab for research details."""
    if not search_results:
        st.info("No sources available for this research.")
        return
    
    # Source quality distribution
    credibility = analysis.get('credibility_analysis', {})
    if credibility:
        st.subheader("📊 Source Quality Distribution")
        
        col1, col2, col3 = st.columns(3)
        dist = credibility.get('credibility_distribution', {})
        
        with col1:
            st.metric("High Quality", dist.get('high', 0), delta_color="normal")
        with col2:
            st.metric("Medium Quality", dist.get('medium', 0), delta_color="normal")
        with col3:
            st.metric("Low Quality", dist.get('low', 0), delta_color="inverse")
        
        st.divider()
    
    # Source list
    st.subheader("📄 Source Details")
    
    for i, source in enumerate(search_results, 1):
        with st.expander(f"📖 Source {i}: {source.get('title', 'Unknown Title')}"):
            st.write(f"**URL:** {source.get('url', 'N/A')}")
            st.write(f"**Relevance Score:** {source.get('score', 0):.2f}")
            st.write("**Content Preview:**")
            content = source.get('content', 'No content available')
            st.write(content[:400] + "..." if len(content) > 400 else content)


def display_report_tab(final_report):
    """Display report tab for research details."""
    if not final_report:
        st.info("No report available for this research.")
        return
    
    # Report metadata
    if final_report.get('generated_at'):
        st.caption(f"Generated: {final_report['generated_at']}")
    
    # Full report
    full_report = final_report.get('full_report', '')
    if full_report:
        st.markdown(full_report)
    else:
        st.warning("Report content not available")


def export_history():
    """Export research history to JSON."""
    if not st.session_state.research_history:
        st.warning("No history to export")
        return
    
    # Prepare export data
    export_data = {
        "exported_at": datetime.now().isoformat(),
        "total_sessions": len(st.session_state.research_history),
        "research_history": st.session_state.research_history
    }
    
    # Convert to JSON
    json_data = json.dumps(export_data, indent=2, default=str)
    
    # Provide download
    st.download_button(
        label="📥 Download History (JSON)",
        data=json_data,
        file_name=f"research_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
        mime="application/json"
    )


def import_history(uploaded_file):
    """Import research history from JSON file."""
    try:
        # Read and parse JSON
        content = uploaded_file.read()
        data = json.loads(content)
        
        # Validate structure
        if "research_history" not in data:
            st.error("Invalid history file format")
            return
        
        imported_history = data["research_history"]
        
        # Merge with existing history (avoid duplicates)
        existing_tasks = {r.get('task', '') for r in st.session_state.research_history}
        new_sessions = [r for r in imported_history if r.get('task', '') not in existing_tasks]
        
        if new_sessions:
            st.session_state.research_history.extend(new_sessions)
            st.success(f"Imported {len(new_sessions)} new research sessions!")
            st.rerun()
        else:
            st.info("No new sessions to import (all sessions already exist)")
    
    except json.JSONDecodeError:
        st.error("Invalid JSON file")
    except Exception as e:
        st.error(f"Import failed: {str(e)}")


if __name__ == "__main__":
    main()
