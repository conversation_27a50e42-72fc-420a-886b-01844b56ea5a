# Multi-Agent Deep Research Assistant

A sophisticated research application powered by LangGraph and Streamlit that uses multiple AI agents to conduct comprehensive research on any topic.

## 🌟 Features

- **Multi-Agent Architecture**: Coordinated team of specialized AI agents
  - 🔍 **Search Agent**: Web research using Tavily API
  - 📊 **Analysis Agent**: Data processing and insight extraction
  - 🎯 **Coordinator Agent**: Workflow orchestration and decision making
  - 📝 **Report Agent**: Comprehensive report generation

- **Advanced Research Capabilities**
  - Multi-round search with query refinement
  - Source credibility analysis
  - Thematic analysis and insight extraction
  - Information gap identification
  - Comprehensive report generation

- **User-Friendly Interface**
  - Clean, intuitive Streamlit interface
  - Real-time progress tracking
  - Research history management
  - Configurable settings
  - Export/import functionality

## 🚀 Quick Start

### Prerequisites

- Python 3.11+
- API keys for:
  - OpenAI or Anthropic (for LLM)
  - Tavily (for web search)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd langgraph_augment_3
   ```

2. **Install dependencies**
   ```bash
   uv sync
   ```

3. **Configure environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

4. **Run the application**
   ```bash
   streamlit run main.py
   ```

## 🔧 Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# API Keys (Required)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
TAVILY_API_KEY=your_tavily_api_key_here

# Model Configuration
DEFAULT_LLM_PROVIDER=openai
DEFAULT_MODEL=gpt-4o
ANTHROPIC_MODEL=claude-3-5-sonnet-20241022

# Application Settings
MAX_SEARCH_RESULTS=10
MAX_RESEARCH_ITERATIONS=3
STREAMLIT_PORT=8501
DEBUG=false
```

### API Keys Setup

1. **OpenAI API Key**
   - Visit [OpenAI Platform](https://platform.openai.com/api-keys)
   - Create a new API key
   - Add to your `.env` file

2. **Anthropic API Key**
   - Visit [Anthropic Console](https://console.anthropic.com/)
   - Create a new API key
   - Add to your `.env` file

3. **Tavily API Key**
   - Visit [Tavily](https://tavily.com/)
   - Sign up and get your API key
   - Add to your `.env` file

## 📖 Usage

### Basic Research

1. **Start Research**
   - Navigate to the 🔍 Research page
   - Enter your research question
   - Configure research parameters
   - Click "🚀 Start Research"

2. **Monitor Progress**
   - Watch real-time updates in the status panel
   - View intermediate results as they're generated
   - Track workflow progress through iterations

3. **Review Results**
   - Explore key insights and themes
   - Review source credibility analysis
   - Download comprehensive reports

### Advanced Features

- **Research History**: View and manage previous research sessions
- **Settings**: Configure API keys, models, and research parameters
- **Export/Import**: Save and share research results

## 🏗️ Architecture

### Multi-Agent System

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Search Agent   │    │ Analysis Agent  │    │ Report Agent    │
│                 │    │                 │    │                 │
│ • Web Search    │    │ • Data Analysis │    │ • Report Gen    │
│ • Query Refine  │    │ • Insight Ext   │    │ • Formatting    │
│ • Result Filter │    │ • Theme ID      │    │ • Export        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ Coordinator     │
                    │ Agent           │
                    │                 │
                    │ • Workflow Mgmt │
                    │ • Decision Make │
                    │ • Quality Check │
                    └─────────────────┘
```

### LangGraph Workflow

The application uses LangGraph to orchestrate the multi-agent workflow:

1. **Initialization**: Coordinator analyzes the research task
2. **Search Phase**: Search agent gathers relevant information
3. **Analysis Phase**: Analysis agent processes and extracts insights
4. **Refinement**: Additional searches based on identified gaps
5. **Report Generation**: Comprehensive report creation
6. **Completion**: Final quality check and delivery

## 🛠️ Development

### Project Structure

```
langgraph_augment_3/
├── agents/                 # AI agent implementations
│   ├── __init__.py
│   ├── base_agent.py      # Base agent class
│   ├── search_agent.py    # Web search agent
│   ├── analysis_agent.py  # Data analysis agent
│   ├── coordinator_agent.py # Workflow coordinator
│   └── report_agent.py    # Report generation agent
├── pages/                 # Streamlit pages
│   ├── 1_🔍_Research.py   # Main research interface
│   ├── 2_📚_History.py    # Research history
│   └── 3_⚙️_Settings.py   # Application settings
├── config.py              # Configuration management
├── workflow.py            # LangGraph workflow definition
├── main.py               # Main Streamlit application
├── .env.example          # Environment variables template
├── pyproject.toml        # Project dependencies
└── README.md             # This file
```

### Adding New Agents

1. Create a new agent class inheriting from `BaseAgent`
2. Implement the `process` method
3. Add the agent to the workflow in `workflow.py`
4. Update the coordinator routing logic

### Extending Functionality

- **New Data Sources**: Add integrations in search agent
- **Analysis Methods**: Extend analysis agent capabilities
- **Report Formats**: Add new output formats in report agent
- **UI Components**: Create new Streamlit pages or components

## 🧪 Testing

### Manual Testing

1. **Basic Functionality**
   ```bash
   streamlit run main.py
   ```
   - Test research workflow
   - Verify all pages load correctly
   - Check settings configuration

2. **API Integration**
   - Verify API keys are working
   - Test search functionality
   - Validate model responses

## 🚀 Deployment

### Local Deployment

```bash
streamlit run main.py --server.port 8501
```

### Docker Deployment

```dockerfile
# Dockerfile example
FROM python:3.11-slim

WORKDIR /app
COPY . .

RUN pip install uv
RUN uv sync

EXPOSE 8501

CMD ["streamlit", "run", "main.py", "--server.address", "0.0.0.0"]
```

### Cloud Deployment

- **Streamlit Cloud**: Connect your GitHub repository
- **Heroku**: Use the provided Procfile
- **AWS/GCP**: Deploy using container services

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- **LangGraph**: For the multi-agent orchestration framework
- **Streamlit**: For the web application framework
- **Tavily**: For the web search API
- **OpenAI/Anthropic**: For the language models

---

**Built with ❤️ using LangGraph and Streamlit**