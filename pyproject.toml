[project]
name = "langgraph-augment-3"
version = "0.1.0"
description = "Multi-agent deep research application using LangGraph and Streamlit"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "langgraph>=0.2.74",
    "streamlit>=1.48.0",
    "tavily-python>=0.3.0",
    "openai>=1.0.0",
    "anthropic>=0.25.0",
    "python-dotenv>=1.0.0",
    "pydantic>=2.0.0",
    "typing-extensions>=4.0.0",
    "langchain-core>=0.3.0",
    "langchain-openai>=0.2.0",
    "langchain-anthropic>=0.2.0",
]
