"""Analysis agent for processing and analyzing research data."""

from typing import List, Dict, Any, Optional
from langchain_core.messages import HumanMessage, AIMessage
from .base_agent import BaseAgent, AgentState
from .search_agent import SearchResult


class AnalysisAgent(BaseAgent):
    """Agent specialized in analyzing and synthesizing research information."""
    
    def __init__(self):
        super().__init__(
            name="AnalysisAgent",
            description="Specialized in analyzing, synthesizing, and extracting insights from research data"
        )
    
    async def process(self, state: AgentState) -> AgentState:
        """Process and analyze search results."""
        search_results = self.get_from_context(state, "search_results", [])
        
        if not search_results:
            state.messages.append(self.create_message("⚠️ No search results available for analysis."))
            return state
        
        # Perform different types of analysis
        key_insights = await self._extract_key_insights(search_results, state.current_task)
        themes = await self._identify_themes(search_results)
        credibility_analysis = await self._analyze_credibility(search_results)
        gaps = await self._identify_information_gaps(search_results, state.current_task)
        
        # Store analysis results
        analysis_results = {
            "key_insights": key_insights,
            "themes": themes,
            "credibility_analysis": credibility_analysis,
            "information_gaps": gaps,
            "total_sources": len(search_results)
        }
        
        state = self.add_to_context(state, "analysis_results", analysis_results)
        
        # Create comprehensive analysis message
        analysis_message = await self._create_analysis_summary(analysis_results)
        state.messages.append(self.create_message(analysis_message))
        
        # Log analysis action
        state = self.log_action(state, "data_analysis", {
            "sources_analyzed": len(search_results),
            "insights_found": len(key_insights),
            "themes_identified": len(themes)
        })
        
        return state
    
    async def _extract_key_insights(self, results: List[SearchResult], task: str) -> List[str]:
        """Extract key insights from search results."""
        # Combine content from top results
        combined_content = "\n".join([
            f"Source: {result.title}\nContent: {result.content}"
            for result in results[:10]  # Analyze top 10 results
        ])
        
        prompt = f"""
        Research Task: {task}
        
        Based on the following sources, extract 5-7 key insights that directly address the research task:
        
        {combined_content}
        
        For each insight:
        1. State the insight clearly
        2. Mention which sources support it
        3. Assess the reliability of the information
        
        Format as a numbered list.
        """
        
        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        insights = [insight.strip() for insight in response.content.split('\n') if insight.strip() and any(char.isdigit() for char in insight[:3])]
        return insights
    
    async def _identify_themes(self, results: List[SearchResult]) -> List[Dict[str, Any]]:
        """Identify common themes across search results."""
        combined_content = "\n".join([result.content for result in results[:15]])
        
        prompt = f"""
        Analyze the following content and identify 3-5 major themes or topics that emerge:
        
        {combined_content}
        
        For each theme, provide:
        - Theme name
        - Brief description
        - How frequently it appears
        - Key supporting points
        
        Format as JSON-like structure.
        """
        
        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        
        # Parse themes (simplified - in production, you might want more robust parsing)
        themes = []
        lines = response.content.split('\n')
        current_theme = {}
        
        for line in lines:
            line = line.strip()
            if line.startswith('-') or line.startswith('•'):
                if current_theme:
                    themes.append(current_theme)
                current_theme = {"name": line[1:].strip(), "description": "", "frequency": "medium", "points": []}
            elif current_theme and line:
                if not current_theme["description"]:
                    current_theme["description"] = line
                else:
                    current_theme["points"].append(line)
        
        if current_theme:
            themes.append(current_theme)
        
        return themes[:5]  # Return top 5 themes
    
    async def _analyze_credibility(self, results: List[SearchResult]) -> Dict[str, Any]:
        """Analyze the credibility of sources."""
        # Simple credibility analysis based on domains and content quality
        high_credibility_domains = ['.edu', '.gov', '.org', 'wikipedia.org', 'reuters.com', 'bbc.com', 'nature.com']
        
        credibility_scores = []
        for result in results:
            score = 0.5  # Base score
            
            # Domain-based scoring
            for domain in high_credibility_domains:
                if domain in result.url.lower():
                    score += 0.3
                    break
            
            # Content length and quality indicators
            if len(result.content) > 200:
                score += 0.1
            if any(word in result.content.lower() for word in ['study', 'research', 'analysis', 'data']):
                score += 0.1
            
            credibility_scores.append(min(score, 1.0))
        
        avg_credibility = sum(credibility_scores) / len(credibility_scores) if credibility_scores else 0
        
        return {
            "average_credibility": avg_credibility,
            "high_credibility_sources": sum(1 for score in credibility_scores if score > 0.7),
            "total_sources": len(results),
            "credibility_distribution": {
                "high": sum(1 for score in credibility_scores if score > 0.7),
                "medium": sum(1 for score in credibility_scores if 0.4 <= score <= 0.7),
                "low": sum(1 for score in credibility_scores if score < 0.4)
            }
        }
    
    async def _identify_information_gaps(self, results: List[SearchResult], task: str) -> List[str]:
        """Identify gaps in the available information."""
        combined_content = "\n".join([result.content for result in results[:10]])
        
        prompt = f"""
        Research Task: {task}
        
        Based on the available information below, identify 3-5 important aspects or questions about the research task that are NOT adequately covered:
        
        Available Information:
        {combined_content}
        
        List the information gaps as specific questions or topics that need more research.
        """
        
        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        gaps = [gap.strip() for gap in response.content.split('\n') if gap.strip() and ('?' in gap or gap.startswith('-'))]
        return gaps[:5]
    
    async def _create_analysis_summary(self, analysis_results: Dict[str, Any]) -> str:
        """Create a comprehensive analysis summary."""
        insights = analysis_results.get("key_insights", [])
        themes = analysis_results.get("themes", [])
        credibility = analysis_results.get("credibility_analysis", {})
        gaps = analysis_results.get("information_gaps", [])
        
        summary = "📊 **Analysis Summary**\n\n"
        
        # Key insights section
        if insights:
            summary += "🔍 **Key Insights:**\n"
            for i, insight in enumerate(insights[:5], 1):
                summary += f"{i}. {insight}\n"
            summary += "\n"
        
        # Themes section
        if themes:
            summary += "🎯 **Major Themes:**\n"
            for theme in themes[:3]:
                summary += f"• **{theme.get('name', 'Unknown')}**: {theme.get('description', 'No description')}\n"
            summary += "\n"
        
        # Credibility section
        if credibility:
            avg_cred = credibility.get("average_credibility", 0)
            high_cred = credibility.get("high_credibility_sources", 0)
            total = credibility.get("total_sources", 0)
            summary += f"🛡️ **Source Credibility:** {avg_cred:.1%} average, {high_cred}/{total} high-credibility sources\n\n"
        
        # Information gaps section
        if gaps:
            summary += "❓ **Information Gaps:**\n"
            for gap in gaps[:3]:
                summary += f"• {gap}\n"
        
        return summary
    
    async def deep_analysis(self, state: AgentState, focus_area: str) -> AgentState:
        """Perform deeper analysis on a specific focus area."""
        search_results = self.get_from_context(state, "search_results", [])
        
        prompt = f"""
        Perform a deep analysis focusing specifically on: {focus_area}
        
        Research Task: {state.current_task}
        
        Available Data: {len(search_results)} sources
        
        Provide:
        1. Detailed findings related to {focus_area}
        2. Supporting evidence and sources
        3. Conflicting viewpoints if any
        4. Confidence level in the findings
        5. Recommendations for further research
        """
        
        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        
        deep_analysis_result = {
            "focus_area": focus_area,
            "analysis": response.content,
            "timestamp": None  # Could add timestamp
        }
        
        # Store deep analysis
        existing_deep_analyses = self.get_from_context(state, "deep_analyses", [])
        existing_deep_analyses.append(deep_analysis_result)
        state = self.add_to_context(state, "deep_analyses", existing_deep_analyses)
        
        # Add message
        state.messages.append(self.create_message(f"🔬 **Deep Analysis - {focus_area}**\n\n{response.content}"))
        
        return state
