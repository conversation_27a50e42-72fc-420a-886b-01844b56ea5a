"""Multi-agent research workflow using LangGraph."""

import asyncio
from typing import Dict, Any, List, Optional, Literal
from langgraph.graph import StateGraph, START, END
from langgraph.types import Command
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage

from agents.base_agent import AgentState
from agents.search_agent import SearchAgent
from agents.analysis_agent import AnalysisAgent
from agents.coordinator_agent import CoordinatorAgent
from agents.report_agent import ReportAgent


class ResearchWorkflow:
    """Multi-agent research workflow orchestrator."""
    
    def __init__(self):
        # Initialize agents
        self.search_agent = SearchAgent()
        self.analysis_agent = AnalysisAgent()
        self.coordinator_agent = CoordinatorAgent()
        self.report_agent = ReportAgent()
        
        # Build the workflow graph
        self.graph = self._build_graph()
    
    def _build_graph(self) -> StateGraph:
        """Build the LangGraph workflow."""
        # Create the state graph
        workflow = StateGraph(AgentState)
        
        # Add nodes for each agent
        workflow.add_node("coordinator", self._coordinator_node)
        workflow.add_node("search", self._search_node)
        workflow.add_node("analysis", self._analysis_node)
        workflow.add_node("report", self._report_node)
        workflow.add_node("human_feedback", self._human_feedback_node)
        
        # Define the workflow edges
        workflow.add_edge(START, "coordinator")
        
        # Add conditional edges from coordinator
        workflow.add_conditional_edges(
            "coordinator",
            self._route_from_coordinator,
            {
                "search": "search",
                "analyze": "analysis", 
                "refine_search": "search",
                "deep_analysis": "analysis",
                "generate_report": "report",
                "complete": END,
                "human_feedback": "human_feedback"
            }
        )
        
        # Add edges back to coordinator from other nodes
        workflow.add_edge("search", "coordinator")
        workflow.add_edge("analysis", "coordinator")
        workflow.add_edge("report", "coordinator")
        workflow.add_edge("human_feedback", "coordinator")
        
        return workflow.compile()
    
    async def _coordinator_node(self, state: AgentState) -> AgentState:
        """Coordinator node that decides next actions."""
        return await self.coordinator_agent.process(state)
    
    async def _search_node(self, state: AgentState) -> AgentState:
        """Search node for web research."""
        next_action = state.context.get("next_action", "search")
        
        if next_action == "refine_search":
            # Get feedback from analysis for refined search
            analysis_results = state.context.get("analysis_results", {})
            gaps = analysis_results.get("information_gaps", [])
            feedback = f"Focus on these information gaps: {'; '.join(gaps[:3])}"
            return await self.search_agent.refine_search(state, feedback)
        else:
            return await self.search_agent.process(state)
    
    async def _analysis_node(self, state: AgentState) -> AgentState:
        """Analysis node for data processing."""
        next_action = state.context.get("next_action", "analyze")
        
        if next_action == "deep_analysis":
            # Perform deep analysis on specific focus area
            themes = state.context.get("analysis_results", {}).get("themes", [])
            if themes:
                focus_area = themes[0].get("name", "main topic")
                return await self.analysis_agent.deep_analysis(state, focus_area)
        
        return await self.analysis_agent.process(state)
    
    async def _report_node(self, state: AgentState) -> AgentState:
        """Report generation node."""
        return await self.report_agent.process(state)
    
    async def _human_feedback_node(self, state: AgentState) -> AgentState:
        """Node for handling human feedback."""
        # This would be implemented to handle human input in the Streamlit interface
        # For now, we'll just pass through
        feedback_message = "Awaiting human feedback..."
        state.messages.append(HumanMessage(content=feedback_message, name="Human"))
        return state
    
    def _route_from_coordinator(self, state: AgentState) -> str:
        """Route from coordinator based on next action."""
        next_action = state.context.get("next_action", "search")
        
        # Map coordinator actions to graph nodes
        action_mapping = {
            "search": "search",
            "analyze": "analysis",
            "refine_search": "search", 
            "deep_analysis": "analysis",
            "generate_report": "report",
            "complete": END,
            "human_feedback": "human_feedback"
        }
        
        return action_mapping.get(next_action, "search")
    
    async def run_research(self, research_task: str, max_iterations: int = 3) -> Dict[str, Any]:
        """Run the complete research workflow."""
        # Initialize state
        initial_state = AgentState(
            messages=[HumanMessage(content=f"Research task: {research_task}", name="User")],
            current_task=research_task,
            context={},
            metadata={"start_time": None}
        )
        
        # Set max iterations in coordinator
        self.coordinator_agent.max_iterations = max_iterations
        
        # Run the workflow
        final_state = await self.graph.ainvoke(initial_state)
        
        # Extract results
        results = {
            "task": research_task,
            "messages": [msg.content for msg in final_state.messages],
            "search_results": final_state.context.get("search_results", []),
            "analysis_results": final_state.context.get("analysis_results", {}),
            "final_report": final_state.context.get("final_report", {}),
            "workflow_metadata": final_state.metadata
        }
        
        return results
    
    async def stream_research(self, research_task: str, max_iterations: int = 3):
        """Stream the research workflow for real-time updates."""
        # Initialize state
        initial_state = AgentState(
            messages=[HumanMessage(content=f"Research task: {research_task}", name="User")],
            current_task=research_task,
            context={},
            metadata={"start_time": None}
        )
        
        # Set max iterations
        self.coordinator_agent.max_iterations = max_iterations
        
        # Stream the workflow
        async for chunk in self.graph.astream(initial_state):
            yield chunk
    
    def get_workflow_status(self) -> Dict[str, Any]:
        """Get current workflow status."""
        return self.coordinator_agent.get_workflow_status()


# Utility functions for Streamlit integration
class WorkflowManager:
    """Manager class for handling workflow in Streamlit."""
    
    def __init__(self):
        self.workflow = ResearchWorkflow()
        self.current_state = None
        self.is_running = False
    
    async def start_research(self, task: str, max_iterations: int = 3) -> Dict[str, Any]:
        """Start a new research task."""
        self.is_running = True
        try:
            results = await self.workflow.run_research(task, max_iterations)
            self.current_state = results
            return results
        finally:
            self.is_running = False
    
    async def stream_research_updates(self, task: str, max_iterations: int = 3):
        """Stream research updates for real-time display."""
        self.is_running = True
        try:
            async for update in self.workflow.stream_research(task, max_iterations):
                yield update
        finally:
            self.is_running = False
    
    def get_current_results(self) -> Optional[Dict[str, Any]]:
        """Get current research results."""
        return self.current_state
    
    def is_workflow_running(self) -> bool:
        """Check if workflow is currently running."""
        return self.is_running
    
    def get_status(self) -> Dict[str, Any]:
        """Get workflow status."""
        status = self.workflow.get_workflow_status()
        status["is_running"] = self.is_running
        return status
