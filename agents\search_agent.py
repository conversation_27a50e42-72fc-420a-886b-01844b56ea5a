"""Search agent for web research using Tavily API."""

import async<PERSON>
from typing import List, Dict, Any, Optional
from tavily import <PERSON>ly<PERSON><PERSON>
from langchain_core.messages import HumanMessage, AIMessage
from .base_agent import BaseAgent, AgentState
from config import config


class SearchResult(dict):
    """Structured search result."""
    
    def __init__(self, title: str, url: str, content: str, score: float = 0.0, **kwargs):
        super().__init__(**kwargs)
        self.title = title
        self.url = url
        self.content = content
        self.score = score
        self.update({
            'title': title,
            'url': url, 
            'content': content,
            'score': score
        })


class SearchAgent(BaseAgent):
    """Agent specialized in web search and information gathering."""
    
    def __init__(self):
        super().__init__(
            name="SearchAgent",
            description="Specialized in web search and information gathering using Tavily API"
        )
        self.tavily_client = TavilyClient(api_key=config.tavily_api_key)
        self.search_history = []
    
    async def process(self, state: AgentState) -> AgentState:
        """Process search requests and gather information."""
        current_task = state.current_task
        if not current_task:
            return state
        
        # Extract search queries from the task
        search_queries = await self._generate_search_queries(current_task)
        
        # Perform searches
        all_results = []
        for query in search_queries:
            results = await self._search_web(query)
            all_results.extend(results)
        
        # Store results in context
        state = self.add_to_context(state, "search_results", all_results)
        state = self.add_to_context(state, "search_queries", search_queries)
        
        # Create summary message
        summary = await self._create_search_summary(search_queries, all_results)
        state.messages.append(self.create_message(summary))
        
        # Log the search action
        state = self.log_action(state, "web_search", {
            "queries": search_queries,
            "results_count": len(all_results)
        })
        
        return state
    
    async def _generate_search_queries(self, task: str) -> List[str]:
        """Generate relevant search queries for the given task."""
        prompt = f"""
        Given the research task: "{task}"
        
        Generate 3-5 specific search queries that would help gather comprehensive information about this topic.
        Focus on different aspects and perspectives.
        
        Return only the search queries, one per line, without numbering or bullets.
        """
        
        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        queries = [q.strip() for q in response.content.split('\n') if q.strip()]
        return queries[:5]  # Limit to 5 queries
    
    async def _search_web(self, query: str) -> List[SearchResult]:
        """Perform web search using Tavily API."""
        try:
            # Use Tavily's search method
            response = self.tavily_client.search(
                query=query,
                search_depth="advanced",
                max_results=config.max_search_results,
                include_answer=True,
                include_raw_content=False
            )
            
            results = []
            for item in response.get('results', []):
                result = SearchResult(
                    title=item.get('title', ''),
                    url=item.get('url', ''),
                    content=item.get('content', ''),
                    score=item.get('score', 0.0)
                )
                results.append(result)
            
            # Store search in history
            self.search_history.append({
                'query': query,
                'results_count': len(results),
                'answer': response.get('answer', '')
            })
            
            return results
            
        except Exception as e:
            print(f"Search error for query '{query}': {str(e)}")
            return []
    
    async def _create_search_summary(self, queries: List[str], results: List[SearchResult]) -> str:
        """Create a summary of search results."""
        if not results:
            return "No search results found for the given queries."
        
        # Prepare results summary
        results_text = "\n".join([
            f"- {result.title}: {result.content[:200]}..." 
            for result in results[:10]  # Limit to top 10 results
        ])
        
        prompt = f"""
        Based on the following search results for queries: {', '.join(queries)}
        
        Search Results:
        {results_text}
        
        Create a concise summary highlighting the key findings and main themes discovered.
        Focus on the most relevant and reliable information.
        """
        
        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        return f"🔍 Search Summary:\n{response.content}"
    
    def get_search_history(self) -> List[Dict[str, Any]]:
        """Get the search history."""
        return self.search_history.copy()
    
    async def refine_search(self, state: AgentState, feedback: str) -> AgentState:
        """Refine search based on feedback."""
        current_results = self.get_from_context(state, "search_results", [])
        
        # Generate refined queries based on feedback
        prompt = f"""
        Previous search found {len(current_results)} results.
        Feedback: {feedback}
        Original task: {state.current_task}
        
        Generate 2-3 more specific search queries to address the feedback and fill information gaps.
        Return only the search queries, one per line.
        """
        
        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        refined_queries = [q.strip() for q in response.content.split('\n') if q.strip()]
        
        # Perform refined searches
        new_results = []
        for query in refined_queries:
            results = await self._search_web(query)
            new_results.extend(results)
        
        # Combine with existing results
        all_results = current_results + new_results
        state = self.add_to_context(state, "search_results", all_results)
        
        # Create refined summary
        summary = await self._create_search_summary(refined_queries, new_results)
        state.messages.append(self.create_message(f"🔍 Refined Search:\n{summary}"))
        
        return state
