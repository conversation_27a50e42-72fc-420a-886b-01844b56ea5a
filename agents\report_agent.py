"""Report generation agent for creating comprehensive research reports."""

from typing import List, Dict, Any, Optional
from datetime import datetime
from langchain_core.messages import HumanMessage, AIMessage
from .base_agent import BaseAgent, AgentState
from .search_agent import SearchResult


class ReportAgent(BaseAgent):
    """Agent specialized in generating comprehensive research reports."""
    
    def __init__(self):
        super().__init__(
            name="ReportAgent",
            description="Specialized in generating comprehensive, well-structured research reports"
        )
    
    async def process(self, state: AgentState) -> AgentState:
        """Generate a comprehensive research report."""
        # Gather all available data
        search_results = self.get_from_context(state, "search_results", [])
        analysis_results = self.get_from_context(state, "analysis_results", {})
        research_evaluation = self.get_from_context(state, "research_evaluation", {})
        
        if not search_results:
            state.messages.append(self.create_message("⚠️ Cannot generate report: No search results available."))
            return state
        
        # Generate different sections of the report
        executive_summary = await self._generate_executive_summary(state, analysis_results)
        methodology = await self._generate_methodology_section(state, search_results)
        findings = await self._generate_findings_section(analysis_results, search_results)
        conclusions = await self._generate_conclusions_section(analysis_results, state.current_task)
        recommendations = await self._generate_recommendations_section(analysis_results, state.current_task)
        sources = await self._generate_sources_section(search_results)
        
        # Compile full report
        full_report = await self._compile_full_report(
            state.current_task,
            executive_summary,
            methodology,
            findings,
            conclusions,
            recommendations,
            sources
        )
        
        # Store report
        report_data = {
            "title": f"Research Report: {state.current_task}",
            "generated_at": datetime.now().isoformat(),
            "executive_summary": executive_summary,
            "methodology": methodology,
            "findings": findings,
            "conclusions": conclusions,
            "recommendations": recommendations,
            "sources": sources,
            "full_report": full_report,
            "metadata": {
                "sources_count": len(search_results),
                "insights_count": len(analysis_results.get("key_insights", [])),
                "themes_count": len(analysis_results.get("themes", []))
            }
        }
        
        state = self.add_to_context(state, "final_report", report_data)
        
        # Add report message
        state.messages.append(self.create_message(f"📋 **Research Report Generated**\n\n{full_report}"))
        
        # Log report generation
        state = self.log_action(state, "report_generation", {
            "sections_generated": 6,
            "total_length": len(full_report),
            "sources_cited": len(search_results)
        })
        
        return state
    
    async def _generate_executive_summary(self, state: AgentState, analysis_results: Dict[str, Any]) -> str:
        """Generate executive summary section."""
        key_insights = analysis_results.get("key_insights", [])
        themes = analysis_results.get("themes", [])
        
        insights_text = "\n".join([f"• {insight}" for insight in key_insights[:5]])
        themes_text = "\n".join([f"• {theme.get('name', 'Unknown')}" for theme in themes[:3]])
        
        prompt = f"""
        Create a concise executive summary for a research report on: {state.current_task}
        
        Key Insights Found:
        {insights_text}
        
        Major Themes:
        {themes_text}
        
        The summary should be 2-3 paragraphs, highlighting the most important findings and their implications.
        """
        
        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        return response.content
    
    async def _generate_methodology_section(self, state: AgentState, search_results: List[SearchResult]) -> str:
        """Generate methodology section."""
        search_queries = self.get_from_context(state, "search_queries", [])
        
        methodology = f"""
        **Research Methodology**
        
        This research was conducted using a systematic multi-agent approach:
        
        1. **Search Strategy**: Conducted comprehensive web searches using {len(search_queries)} targeted queries
        2. **Source Collection**: Gathered {len(search_results)} sources from various domains
        3. **Analysis Process**: Applied automated content analysis to identify patterns, themes, and insights
        4. **Quality Assessment**: Evaluated source credibility and information reliability
        5. **Synthesis**: Integrated findings from multiple sources to provide comprehensive coverage
        
        **Search Queries Used:**
        {chr(10).join([f"• {query}" for query in search_queries[:10]])}
        """
        
        return methodology
    
    async def _generate_findings_section(self, analysis_results: Dict[str, Any], search_results: List[SearchResult]) -> str:
        """Generate findings section."""
        key_insights = analysis_results.get("key_insights", [])
        themes = analysis_results.get("themes", [])
        credibility = analysis_results.get("credibility_analysis", {})
        
        findings = "**Key Findings**\n\n"
        
        # Add insights
        if key_insights:
            findings += "**Primary Insights:**\n"
            for i, insight in enumerate(key_insights, 1):
                findings += f"{i}. {insight}\n"
            findings += "\n"
        
        # Add themes
        if themes:
            findings += "**Major Themes Identified:**\n"
            for theme in themes:
                findings += f"• **{theme.get('name', 'Unknown')}**: {theme.get('description', 'No description')}\n"
            findings += "\n"
        
        # Add credibility assessment
        if credibility:
            avg_cred = credibility.get("average_credibility", 0)
            high_cred = credibility.get("high_credibility_sources", 0)
            total = credibility.get("total_sources", 0)
            findings += f"**Source Quality Assessment:**\n"
            findings += f"• Average credibility score: {avg_cred:.1%}\n"
            findings += f"• High-credibility sources: {high_cred} out of {total}\n"
        
        return findings
    
    async def _generate_conclusions_section(self, analysis_results: Dict[str, Any], task: str) -> str:
        """Generate conclusions section."""
        key_insights = analysis_results.get("key_insights", [])
        gaps = analysis_results.get("information_gaps", [])
        
        insights_text = "\n".join([f"• {insight}" for insight in key_insights[:5]])
        gaps_text = "\n".join([f"• {gap}" for gap in gaps[:3]])
        
        prompt = f"""
        Based on the research conducted on "{task}", write a conclusions section that:
        
        Key Insights:
        {insights_text}
        
        Information Gaps:
        {gaps_text}
        
        1. Synthesizes the main findings
        2. Addresses the original research question/task
        3. Acknowledges limitations and gaps
        4. Provides overall assessment
        
        Keep it concise but comprehensive.
        """
        
        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        return response.content
    
    async def _generate_recommendations_section(self, analysis_results: Dict[str, Any], task: str) -> str:
        """Generate recommendations section."""
        gaps = analysis_results.get("information_gaps", [])
        
        prompt = f"""
        Based on the research on "{task}" and the identified information gaps, provide:
        
        Information Gaps Identified:
        {chr(10).join([f"• {gap}" for gap in gaps[:5]])}
        
        1. 3-5 actionable recommendations based on the findings
        2. Suggestions for further research
        3. Areas that need additional investigation
        
        Format as clear, numbered recommendations.
        """
        
        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        return response.content
    
    async def _generate_sources_section(self, search_results: List[SearchResult]) -> str:
        """Generate sources section."""
        sources = "**Sources**\n\n"
        
        # Group sources by domain for better organization
        domain_groups = {}
        for result in search_results[:20]:  # Limit to top 20 sources
            domain = result.url.split('/')[2] if '/' in result.url else result.url
            if domain not in domain_groups:
                domain_groups[domain] = []
            domain_groups[domain].append(result)
        
        for i, result in enumerate(search_results[:20], 1):
            sources += f"{i}. **{result.title}**\n"
            sources += f"   URL: {result.url}\n"
            if hasattr(result, 'score') and result.score:
                sources += f"   Relevance Score: {result.score:.2f}\n"
            sources += "\n"
        
        return sources
    
    async def _compile_full_report(self, task: str, executive_summary: str, methodology: str, 
                                 findings: str, conclusions: str, recommendations: str, sources: str) -> str:
        """Compile the full report."""
        report = f"""
# Research Report: {task}

**Generated on:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

---

## Executive Summary

{executive_summary}

---

## {methodology}

---

## {findings}

---

## Conclusions

{conclusions}

---

## Recommendations

{recommendations}

---

## {sources}

---

*This report was generated using an automated multi-agent research system.*
        """.strip()
        
        return report
    
    async def generate_summary_report(self, state: AgentState) -> AgentState:
        """Generate a shorter summary report."""
        analysis_results = self.get_from_context(state, "analysis_results", {})
        search_results = self.get_from_context(state, "search_results", [])
        
        key_insights = analysis_results.get("key_insights", [])[:3]
        themes = analysis_results.get("themes", [])[:2]
        
        summary_report = f"""
📋 **Research Summary: {state.current_task}**

🔍 **Key Findings:**
{chr(10).join([f"• {insight}" for insight in key_insights])}

🎯 **Main Themes:**
{chr(10).join([f"• {theme.get('name', 'Unknown')}: {theme.get('description', '')[:100]}..." for theme in themes])}

📊 **Research Scope:**
• Sources analyzed: {len(search_results)}
• Insights extracted: {len(analysis_results.get('key_insights', []))}
• Themes identified: {len(analysis_results.get('themes', []))}

*Generated on {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}*
        """.strip()
        
        state = self.add_to_context(state, "summary_report", summary_report)
        state.messages.append(self.create_message(summary_report))
        
        return state
