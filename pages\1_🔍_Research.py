"""Research page for the multi-agent research application."""

import streamlit as st
import asyncio
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from config import config
from workflow import WorkflowManager


def main():
    """Main research page."""
    st.set_page_config(
        page_title="Research - Multi-Agent Assistant",
        page_icon="🔍",
        layout="wide"
    )
    
    st.title("🔍 Deep Research")
    st.markdown("*Conduct comprehensive research using multiple AI agents*")
    
    # Initialize session state
    if "workflow_manager" not in st.session_state:
        st.session_state.workflow_manager = WorkflowManager()
    
    if "research_results" not in st.session_state:
        st.session_state.research_results = None
    
    if "research_running" not in st.session_state:
        st.session_state.research_running = False
    
    # Research configuration
    with st.sidebar:
        st.header("🛠️ Research Configuration")
        
        # Validate API keys
        try:
            config.validate_api_keys()
            st.success("✅ API keys configured")
        except ValueError as e:
            st.error(f"❌ {str(e)}")
            st.stop()
        
        # Research parameters
        max_iterations = st.slider("Research Depth", 1, 5, 3, 
                                 help="Number of research iterations")
        
        search_depth = st.selectbox("Search Depth", 
                                  ["basic", "advanced"], 
                                  index=1,
                                  help="Depth of web search")
        
        include_analysis = st.checkbox("Include Deep Analysis", True,
                                     help="Perform detailed analysis of findings")
        
        auto_generate_report = st.checkbox("Auto-Generate Report", True,
                                         help="Automatically generate final report")
    
    # Main research interface
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("📝 Research Query")
        
        # Research form
        with st.form("research_form", clear_on_submit=False):
            research_query = st.text_area(
                "What would you like to research?",
                placeholder="Enter your research question or topic here...\n\nExamples:\n• Latest developments in AI safety\n• Impact of climate change on agriculture\n• Quantum computing applications in finance",
                height=150,
                help="Be specific for better results"
            )
            
            # Advanced options
            with st.expander("🔧 Advanced Options"):
                focus_areas = st.text_input(
                    "Focus Areas (comma-separated)",
                    placeholder="e.g., technical aspects, market impact, future trends",
                    help="Specific areas to focus the research on"
                )
                
                exclude_terms = st.text_input(
                    "Exclude Terms (comma-separated)",
                    placeholder="e.g., outdated, speculation",
                    help="Terms to avoid in search results"
                )
                
                time_range = st.selectbox(
                    "Time Range",
                    ["Any time", "Past year", "Past 6 months", "Past month"],
                    help="Limit search to recent content"
                )
            
            # Submit button
            col_submit, col_clear = st.columns([1, 1])
            with col_submit:
                submitted = st.form_submit_button("🚀 Start Research", 
                                                type="primary",
                                                use_container_width=True)
            with col_clear:
                if st.form_submit_button("🗑️ Clear", use_container_width=True):
                    st.session_state.research_results = None
                    st.rerun()
        
        # Handle form submission
        if submitted and research_query.strip():
            st.session_state.current_query = research_query.strip()
            st.session_state.research_config = {
                "max_iterations": max_iterations,
                "search_depth": search_depth,
                "include_analysis": include_analysis,
                "auto_generate_report": auto_generate_report,
                "focus_areas": [area.strip() for area in focus_areas.split(",") if area.strip()],
                "exclude_terms": [term.strip() for term in exclude_terms.split(",") if term.strip()],
                "time_range": time_range
            }
            st.session_state.research_running = True
            st.rerun()
    
    with col2:
        st.header("📊 Research Status")
        
        # Status display
        if st.session_state.research_running:
            st.info("🔄 Research in progress...")
            
            # Progress bar
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            # Run research
            run_research_async()
            
        else:
            # Show workflow status
            status = st.session_state.workflow_manager.get_status()
            
            st.metric("Workflow State", status.get('state', 'idle').title())
            
            if status.get('iteration', 0) > 0:
                st.metric("Progress", 
                         f"{status.get('iteration', 0)}/{status.get('max_iterations', 3)}")
            
            # Quick stats if results available
            if st.session_state.research_results:
                results = st.session_state.research_results
                search_results = results.get('search_results', [])
                analysis = results.get('analysis_results', {})
                
                st.metric("Sources Found", len(search_results))
                st.metric("Key Insights", len(analysis.get('key_insights', [])))
    
    # Display research results
    if st.session_state.research_results and not st.session_state.research_running:
        display_research_results()


def run_research_async():
    """Run research workflow asynchronously."""
    try:
        # This would be implemented with proper async handling in a real app
        # For now, we'll simulate the research process
        
        query = st.session_state.current_query
        config = st.session_state.research_config
        
        # Simulate research workflow
        with st.spinner("Conducting research..."):
            # In a real implementation, this would call the actual workflow
            import time
            time.sleep(2)  # Simulate processing time
            
            # Mock results for demonstration
            mock_results = create_mock_results(query)
            st.session_state.research_results = mock_results
            st.session_state.research_running = False
            
        st.success("✅ Research completed!")
        st.rerun()
        
    except Exception as e:
        st.error(f"Research failed: {str(e)}")
        st.session_state.research_running = False


def create_mock_results(query):
    """Create mock results for demonstration."""
    return {
        "task": query,
        "search_results": [
            {
                "title": f"Research Article on {query}",
                "url": "https://example.com/article1",
                "content": f"This article discusses various aspects of {query} and provides comprehensive insights...",
                "score": 0.95
            },
            {
                "title": f"Latest Developments in {query}",
                "url": "https://example.com/article2", 
                "content": f"Recent developments in {query} show promising trends and applications...",
                "score": 0.87
            }
        ],
        "analysis_results": {
            "key_insights": [
                f"Key finding 1 about {query}",
                f"Important trend identified in {query}",
                f"Critical consideration for {query}"
            ],
            "themes": [
                {
                    "name": "Primary Theme",
                    "description": f"Main theme related to {query}",
                    "points": ["Point 1", "Point 2", "Point 3"]
                }
            ],
            "credibility_analysis": {
                "average_credibility": 0.85,
                "high_credibility_sources": 2,
                "total_sources": 2,
                "credibility_distribution": {"high": 2, "medium": 0, "low": 0}
            },
            "information_gaps": [
                f"Need more recent data on {query}",
                f"Limited information on practical applications"
            ]
        },
        "final_report": {
            "title": f"Research Report: {query}",
            "full_report": f"# Research Report: {query}\n\nThis comprehensive report covers...",
            "generated_at": "2024-01-01T12:00:00"
        }
    }


def display_research_results():
    """Display comprehensive research results."""
    st.header("📋 Research Results")
    
    results = st.session_state.research_results
    
    # Results summary
    with st.container():
        st.subheader("📊 Summary")
        
        col1, col2, col3, col4 = st.columns(4)
        
        search_results = results.get('search_results', [])
        analysis = results.get('analysis_results', {})
        
        with col1:
            st.metric("Sources", len(search_results))
        with col2:
            st.metric("Insights", len(analysis.get('key_insights', [])))
        with col3:
            st.metric("Themes", len(analysis.get('themes', [])))
        with col4:
            credibility = analysis.get('credibility_analysis', {})
            avg_cred = credibility.get('average_credibility', 0)
            st.metric("Quality", f"{avg_cred:.0%}")
    
    # Tabbed results view
    tab1, tab2, tab3 = st.tabs(["💡 Key Insights", "📚 Sources", "📄 Full Report"])
    
    with tab1:
        display_insights_tab(analysis)
    
    with tab2:
        display_sources_tab(search_results, analysis)
    
    with tab3:
        display_report_tab(results.get('final_report', {}))


def display_insights_tab(analysis):
    """Display key insights tab."""
    insights = analysis.get('key_insights', [])
    themes = analysis.get('themes', [])
    gaps = analysis.get('information_gaps', [])
    
    if insights:
        st.subheader("🔍 Key Insights")
        for i, insight in enumerate(insights, 1):
            st.write(f"**{i}.** {insight}")
        st.divider()
    
    if themes:
        st.subheader("🎯 Major Themes")
        for theme in themes:
            with st.expander(f"📌 {theme.get('name', 'Unknown Theme')}"):
                st.write(theme.get('description', 'No description available'))
                points = theme.get('points', [])
                if points:
                    st.write("**Key Points:**")
                    for point in points:
                        st.write(f"• {point}")
        st.divider()
    
    if gaps:
        st.subheader("❓ Information Gaps")
        st.info("Areas that may benefit from additional research:")
        for gap in gaps:
            st.write(f"• {gap}")


def display_sources_tab(search_results, analysis):
    """Display sources tab."""
    if not search_results:
        st.info("No sources available")
        return
    
    # Source quality overview
    credibility = analysis.get('credibility_analysis', {})
    if credibility:
        st.subheader("📊 Source Quality Overview")
        
        col1, col2, col3 = st.columns(3)
        dist = credibility.get('credibility_distribution', {})
        
        with col1:
            st.metric("High Quality", dist.get('high', 0), delta_color="normal")
        with col2:
            st.metric("Medium Quality", dist.get('medium', 0), delta_color="normal") 
        with col3:
            st.metric("Low Quality", dist.get('low', 0), delta_color="inverse")
        
        st.divider()
    
    # Individual sources
    st.subheader("📄 Source Details")
    
    for i, source in enumerate(search_results, 1):
        with st.expander(f"📖 Source {i}: {source.get('title', 'Unknown Title')}"):
            col1, col2 = st.columns([3, 1])
            
            with col1:
                st.write(f"**URL:** {source.get('url', 'N/A')}")
                st.write("**Content Preview:**")
                content = source.get('content', 'No content available')
                st.write(content[:300] + "..." if len(content) > 300 else content)
            
            with col2:
                score = source.get('score', 0)
                st.metric("Relevance", f"{score:.1%}")
                
                # Quality indicator
                if score > 0.8:
                    st.success("High Quality")
                elif score > 0.6:
                    st.warning("Medium Quality")
                else:
                    st.error("Low Quality")


def display_report_tab(report_data):
    """Display full report tab."""
    if not report_data:
        st.info("No report available")
        return
    
    st.subheader("📄 Complete Research Report")
    
    # Report metadata
    if report_data.get('generated_at'):
        st.caption(f"Generated: {report_data['generated_at']}")
    
    # Full report content
    full_report = report_data.get('full_report', '')
    if full_report:
        st.markdown(full_report)
        
        # Download button
        st.download_button(
            label="📥 Download Report (Markdown)",
            data=full_report,
            file_name=f"research_report_{report_data.get('title', 'report').replace(' ', '_').replace(':', '')}.md",
            mime="text/markdown",
            use_container_width=True
        )
    else:
        st.warning("Report content not available")


if __name__ == "__main__":
    main()
