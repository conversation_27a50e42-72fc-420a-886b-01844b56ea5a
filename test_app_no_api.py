"""Test script that doesn't require API keys - tests basic structure only."""

import sys
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

def test_imports():
    """Test that all modules can be imported."""
    print("🔧 Testing Module Imports...")
    
    try:
        # Test config import
        from config import config
        print("✅ Config module imported")
        
        # Test agent imports
        from agents.base_agent import BaseAgent, AgentState
        from agents.search_agent import SearchAgent
        from agents.analysis_agent import AnalysisAgent
        from agents.coordinator_agent import CoordinatorAgent
        from agents.report_agent import ReportAgent
        print("✅ All agent modules imported")
        
        # Test workflow import
        from workflow import WorkflowManager
        print("✅ Workflow module imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {str(e)}")
        return False


def test_file_structure():
    """Test that all required files exist."""
    print("\n📁 Testing File Structure...")
    
    required_files = [
        "config.py",
        "workflow.py", 
        "main.py",
        ".env.example",
        "pyproject.toml",
        "README.md",
        "agents/__init__.py",
        "agents/base_agent.py",
        "agents/search_agent.py",
        "agents/analysis_agent.py",
        "agents/coordinator_agent.py",
        "agents/report_agent.py",
        "pages/1_🔍_Research.py",
        "pages/2_📚_History.py",
        "pages/3_⚙️_Settings.py"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MISSING")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ Missing {len(missing_files)} files")
        return False
    else:
        print(f"\n✅ All {len(required_files)} required files present")
        return True


def test_config_structure():
    """Test configuration structure without API keys."""
    print("\n⚙️ Testing Configuration Structure...")
    
    try:
        from config import config
        
        # Test config attributes exist
        attributes = [
            'default_llm_provider',
            'default_model',
            'max_search_results',
            'max_research_iterations',
            'debug'
        ]
        
        for attr in attributes:
            if hasattr(config, attr):
                value = getattr(config, attr)
                print(f"✅ {attr}: {value}")
            else:
                print(f"❌ {attr}: Missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Config structure test failed: {str(e)}")
        return False


def test_agent_classes():
    """Test agent class definitions without instantiation."""
    print("\n🤖 Testing Agent Class Definitions...")
    
    try:
        from agents.base_agent import BaseAgent
        from agents.search_agent import SearchAgent
        from agents.analysis_agent import AnalysisAgent
        from agents.coordinator_agent import CoordinatorAgent
        from agents.report_agent import ReportAgent
        
        # Test class inheritance
        agents = [SearchAgent, AnalysisAgent, CoordinatorAgent, ReportAgent]
        
        for agent_class in agents:
            if issubclass(agent_class, BaseAgent):
                print(f"✅ {agent_class.__name__} inherits from BaseAgent")
            else:
                print(f"❌ {agent_class.__name__} doesn't inherit from BaseAgent")
                return False
        
        # Test required methods exist
        required_methods = ['process']
        
        for agent_class in agents:
            for method in required_methods:
                if hasattr(agent_class, method):
                    print(f"✅ {agent_class.__name__}.{method} exists")
                else:
                    print(f"❌ {agent_class.__name__}.{method} missing")
                    return False
        
        return True
        
    except Exception as e:
        print(f"❌ Agent class test failed: {str(e)}")
        return False


def test_streamlit_structure():
    """Test Streamlit application structure."""
    print("\n📱 Testing Streamlit Structure...")
    
    try:
        # Check main.py has streamlit imports
        with open("main.py", "r", encoding="utf-8") as f:
            main_content = f.read()
            
        if "import streamlit as st" in main_content:
            print("✅ Main app imports Streamlit")
        else:
            print("❌ Main app missing Streamlit import")
            return False
        
        if "st.set_page_config" in main_content:
            print("✅ Main app configures page")
        else:
            print("❌ Main app missing page config")
            return False
        
        # Check page files
        page_files = [
            "pages/1_🔍_Research.py",
            "pages/2_📚_History.py",
            "pages/3_⚙️_Settings.py"
        ]
        
        for page_file in page_files:
            with open(page_file, "r", encoding="utf-8") as f:
                page_content = f.read()
                
            if "import streamlit as st" in page_content:
                print(f"✅ {page_file} imports Streamlit")
            else:
                print(f"❌ {page_file} missing Streamlit import")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Streamlit structure test failed: {str(e)}")
        return False


def run_all_tests():
    """Run all tests that don't require API keys."""
    print("🧪 Starting Structure Tests (No API Keys Required)...\n")
    
    tests = [
        ("Module Imports", test_imports),
        ("File Structure", test_file_structure),
        ("Configuration Structure", test_config_structure),
        ("Agent Class Definitions", test_agent_classes),
        ("Streamlit Structure", test_streamlit_structure)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 Test Summary:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"Total: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All structure tests passed!")
        print("\n📝 Next Steps:")
        print("1. Configure API keys in .env file")
        print("2. Run: streamlit run main.py")
        print("3. Test the full application")
        return True
    else:
        print("⚠️  Some structure tests failed.")
        return False


def print_setup_instructions():
    """Print setup instructions."""
    print("\n🚀 Setup Instructions:")
    print("=" * 60)
    print("1. Copy .env.example to .env:")
    print("   cp .env.example .env")
    print("\n2. Edit .env file with your API keys:")
    print("   - TAVILY_API_KEY=your_tavily_key")
    print("   - OPENAI_API_KEY=your_openai_key (or ANTHROPIC_API_KEY)")
    print("\n3. Run the application:")
    print("   streamlit run main.py")
    print("\n🔑 Get API Keys:")
    print("   - Tavily: https://tavily.com/")
    print("   - OpenAI: https://platform.openai.com/api-keys")
    print("   - Anthropic: https://console.anthropic.com/")


if __name__ == "__main__":
    success = run_all_tests()
    
    if success:
        print_setup_instructions()
    
    sys.exit(0 if success else 1)
