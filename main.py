"""Multi-Agent Deep Research Application using LangGraph and Streamlit."""

import streamlit as st
import asyncio
from pathlib import Path

# Add the current directory to Python path for imports
import sys
sys.path.append(str(Path(__file__).parent))

from config import config
from workflow import WorkflowManager


def main():
    """Main entry point for the Streamlit application."""
    st.set_page_config(
        page_title="Multi-Agent Research Assistant",
        page_icon="🔬",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # Initialize session state
    if "workflow_manager" not in st.session_state:
        st.session_state.workflow_manager = WorkflowManager()

    if "research_history" not in st.session_state:
        st.session_state.research_history = []

    if "current_research" not in st.session_state:
        st.session_state.current_research = None

    # Main app interface
    st.title("🔬 Multi-Agent Deep Research Assistant")
    st.markdown("*Powered by LangGraph and multiple AI agents*")

    # Sidebar for configuration and history
    with st.sidebar:
        st.header("⚙️ Configuration")

        # API Key validation
        try:
            config.validate_api_keys()
            st.success("✅ API keys configured")
        except ValueError as e:
            st.error(f"❌ {str(e)}")
            st.info("Please set your API keys in the .env file")
            return

        # Research settings
        st.subheader("Research Settings")
        max_iterations = st.slider("Max Research Iterations", 1, 5, 3)
        model_provider = st.selectbox("LLM Provider", ["openai", "anthropic"],
                                    index=0 if config.default_llm_provider == "openai" else 1)

        # Research history
        st.subheader("📚 Research History")
        if st.session_state.research_history:
            for i, research in enumerate(reversed(st.session_state.research_history[-5:])):
                if st.button(f"📄 {research['task'][:30]}...", key=f"history_{i}"):
                    st.session_state.current_research = research
        else:
            st.info("No research history yet")

    # Main content area
    col1, col2 = st.columns([2, 1])

    with col1:
        st.header("🎯 Research Query")

        # Research input form
        with st.form("research_form"):
            research_task = st.text_area(
                "Enter your research question or topic:",
                placeholder="e.g., What are the latest developments in quantum computing applications for cryptography?",
                height=100
            )

            submitted = st.form_submit_button("🚀 Start Research", type="primary")

            if submitted and research_task:
                # Start research workflow
                st.session_state.current_task = research_task
                st.session_state.research_running = True
                st.rerun()

    with col2:
        st.header("📊 Workflow Status")

        # Display workflow status
        if hasattr(st.session_state, 'workflow_manager'):
            status = st.session_state.workflow_manager.get_status()

            # Progress indicator
            progress = status.get('progress', 0)
            st.progress(progress)

            # Status details
            st.metric("Current State", status.get('state', 'idle'))
            st.metric("Iteration", f"{status.get('iteration', 0)}/{status.get('max_iterations', 3)}")

            if status.get('is_running', False):
                st.info("🔄 Research in progress...")

    # Research results display
    if hasattr(st.session_state, 'research_running') and st.session_state.research_running:
        st.header("🔍 Research Progress")

        # Create placeholder for streaming updates
        progress_placeholder = st.empty()
        results_placeholder = st.empty()

        # Run research workflow
        asyncio.run(run_research_workflow(
            st.session_state.current_task,
            max_iterations,
            progress_placeholder,
            results_placeholder
        ))

        st.session_state.research_running = False

    # Display current research results
    if st.session_state.current_research:
        display_research_results(st.session_state.current_research)


async def run_research_workflow(task, max_iterations, progress_placeholder, results_placeholder):
    """Run the research workflow with streaming updates."""
    workflow_manager = st.session_state.workflow_manager

    try:
        # Stream research updates
        async for update in workflow_manager.stream_research_updates(task, max_iterations):
            # Update progress display
            with progress_placeholder.container():
                st.write(f"**Update:** {update}")

            # Update results as they come in
            current_results = workflow_manager.get_current_results()
            if current_results:
                with results_placeholder.container():
                    display_partial_results(current_results)

        # Get final results
        final_results = workflow_manager.get_current_results()
        if final_results:
            # Add to history
            st.session_state.research_history.append(final_results)
            st.session_state.current_research = final_results

            # Clear progress display
            progress_placeholder.empty()

    except Exception as e:
        st.error(f"Research failed: {str(e)}")


def display_partial_results(results):
    """Display partial research results during streaming."""
    if not results:
        return

    # Display search results if available
    search_results = results.get('search_results', [])
    if search_results:
        st.subheader(f"🔍 Found {len(search_results)} sources")

        # Show top 3 sources
        for i, result in enumerate(search_results[:3]):
            with st.expander(f"📄 {result.get('title', 'Unknown Title')}"):
                st.write(f"**URL:** {result.get('url', 'N/A')}")
                st.write(f"**Content:** {result.get('content', 'N/A')[:200]}...")

    # Display analysis if available
    analysis = results.get('analysis_results', {})
    if analysis:
        insights = analysis.get('key_insights', [])
        if insights:
            st.subheader("💡 Key Insights")
            for insight in insights[:3]:
                st.write(f"• {insight}")


def display_research_results(results):
    """Display complete research results."""
    st.header("📋 Research Results")

    # Create tabs for different views
    tab1, tab2, tab3, tab4 = st.tabs(["📊 Summary", "🔍 Sources", "📈 Analysis", "📄 Full Report"])

    with tab1:
        display_summary_tab(results)

    with tab2:
        display_sources_tab(results)

    with tab3:
        display_analysis_tab(results)

    with tab4:
        display_report_tab(results)


def display_summary_tab(results):
    """Display summary tab content."""
    st.subheader("🎯 Research Summary")

    # Key metrics
    col1, col2, col3, col4 = st.columns(4)

    search_results = results.get('search_results', [])
    analysis = results.get('analysis_results', {})

    with col1:
        st.metric("Sources Found", len(search_results))

    with col2:
        st.metric("Key Insights", len(analysis.get('key_insights', [])))

    with col3:
        st.metric("Themes Identified", len(analysis.get('themes', [])))

    with col4:
        credibility = analysis.get('credibility_analysis', {})
        avg_cred = credibility.get('average_credibility', 0)
        st.metric("Avg. Source Quality", f"{avg_cred:.1%}")

    # Key insights
    insights = analysis.get('key_insights', [])
    if insights:
        st.subheader("💡 Key Insights")
        for i, insight in enumerate(insights, 1):
            st.write(f"{i}. {insight}")

    # Major themes
    themes = analysis.get('themes', [])
    if themes:
        st.subheader("🎯 Major Themes")
        for theme in themes:
            st.write(f"**{theme.get('name', 'Unknown')}:** {theme.get('description', 'No description')}")


def display_sources_tab(results):
    """Display sources tab content."""
    st.subheader("📚 Research Sources")

    search_results = results.get('search_results', [])

    if not search_results:
        st.info("No sources available")
        return

    # Source quality distribution
    analysis = results.get('analysis_results', {})
    credibility = analysis.get('credibility_analysis', {})

    if credibility:
        st.subheader("📊 Source Quality Distribution")
        dist = credibility.get('credibility_distribution', {})

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("High Quality", dist.get('high', 0), delta_color="normal")
        with col2:
            st.metric("Medium Quality", dist.get('medium', 0), delta_color="normal")
        with col3:
            st.metric("Low Quality", dist.get('low', 0), delta_color="inverse")

    # List all sources
    st.subheader("📄 All Sources")
    for i, result in enumerate(search_results, 1):
        with st.expander(f"{i}. {result.get('title', 'Unknown Title')}"):
            st.write(f"**URL:** {result.get('url', 'N/A')}")
            st.write(f"**Relevance Score:** {result.get('score', 0):.2f}")
            st.write(f"**Content Preview:**")
            st.write(result.get('content', 'N/A')[:500] + "...")


def display_analysis_tab(results):
    """Display analysis tab content."""
    st.subheader("📈 Detailed Analysis")

    analysis = results.get('analysis_results', {})

    if not analysis:
        st.info("No analysis available")
        return

    # Themes analysis
    themes = analysis.get('themes', [])
    if themes:
        st.subheader("🎯 Thematic Analysis")
        for theme in themes:
            st.write(f"**{theme.get('name', 'Unknown Theme')}**")
            st.write(theme.get('description', 'No description'))

            points = theme.get('points', [])
            if points:
                for point in points[:3]:
                    st.write(f"• {point}")
            st.write("---")

    # Information gaps
    gaps = analysis.get('information_gaps', [])
    if gaps:
        st.subheader("❓ Information Gaps")
        st.info("Areas that may need additional research:")
        for gap in gaps:
            st.write(f"• {gap}")


def display_report_tab(results):
    """Display full report tab content."""
    st.subheader("📄 Full Research Report")

    final_report = results.get('final_report', {})

    if not final_report:
        st.info("No full report available")
        return

    # Display the full report
    full_report_text = final_report.get('full_report', '')
    if full_report_text:
        st.markdown(full_report_text)

        # Download button
        st.download_button(
            label="📥 Download Report",
            data=full_report_text,
            file_name=f"research_report_{results.get('task', 'unknown').replace(' ', '_')}.md",
            mime="text/markdown"
        )
    else:
        st.info("Report content not available")


if __name__ == "__main__":
    main()
