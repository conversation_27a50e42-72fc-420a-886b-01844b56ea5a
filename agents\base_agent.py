"""Base agent class for the multi-agent research system."""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain_core.language_models import BaseChatModel
from langchain_openai import Chat<PERSON>penAI
from langchain_anthropic import ChatAnthropic
from config import config


class AgentState(BaseModel):
    """Base state for agents."""
    messages: List[BaseMessage] = Field(default_factory=list)
    current_task: Optional[str] = None
    context: Dict[str, Any] = Field(default_factory=dict)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class BaseAgent(ABC):
    """Base class for all agents in the research system."""
    
    def __init__(self, name: str, description: str, model_provider: Optional[str] = None):
        self.name = name
        self.description = description
        self.model_provider = model_provider or config.default_llm_provider
        self.llm = self._initialize_llm()
        
    def _initialize_llm(self) -> BaseChatModel:
        """Initialize the language model based on configuration."""
        if self.model_provider == "openai":
            return ChatOpenAI(
                model=config.default_model,
                api_key=config.openai_api_key,
                temperature=0.1
            )
        elif self.model_provider == "anthropic":
            return ChatAnthropic(
                model=config.anthropic_model,
                api_key=config.anthropic_api_key,
                temperature=0.1
            )
        else:
            raise ValueError(f"Unsupported model provider: {self.model_provider}")
    
    @abstractmethod
    async def process(self, state: AgentState) -> AgentState:
        """Process the current state and return updated state."""
        pass
    
    def create_message(self, content: str, message_type: str = "ai") -> BaseMessage:
        """Create a message with the agent's name."""
        if message_type == "human":
            return HumanMessage(content=content, name=self.name)
        else:
            return AIMessage(content=content, name=self.name)
    
    def add_to_context(self, state: AgentState, key: str, value: Any) -> AgentState:
        """Add information to the agent's context."""
        state.context[key] = value
        return state
    
    def get_from_context(self, state: AgentState, key: str, default: Any = None) -> Any:
        """Get information from the agent's context."""
        return state.context.get(key, default)
    
    def log_action(self, state: AgentState, action: str, details: Dict[str, Any] = None) -> AgentState:
        """Log an action taken by the agent."""
        if "actions" not in state.metadata:
            state.metadata["actions"] = []
        
        action_log = {
            "agent": self.name,
            "action": action,
            "details": details or {},
            "timestamp": None  # Could add timestamp if needed
        }
        state.metadata["actions"].append(action_log)
        return state
