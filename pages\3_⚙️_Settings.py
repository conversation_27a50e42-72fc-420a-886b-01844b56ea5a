"""Settings page for the multi-agent research application."""

import streamlit as st
import os
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from config import config


def main():
    """Main settings page."""
    st.set_page_config(
        page_title="Settings - Multi-Agent Assistant",
        page_icon="⚙️",
        layout="wide"
    )
    
    st.title("⚙️ Application Settings")
    st.markdown("*Configure your multi-agent research assistant*")
    
    # Initialize session state for settings
    if "settings_changed" not in st.session_state:
        st.session_state.settings_changed = False
    
    # Create tabs for different setting categories
    tab1, tab2, tab3, tab4 = st.tabs(["🔑 API Keys", "🤖 Models", "🔍 Research", "🎨 Interface"])
    
    with tab1:
        display_api_settings()
    
    with tab2:
        display_model_settings()
    
    with tab3:
        display_research_settings()
    
    with tab4:
        display_interface_settings()
    
    # Settings actions
    st.divider()
    display_settings_actions()


def display_api_settings():
    """Display API key configuration."""
    st.header("🔑 API Key Configuration")
    st.markdown("Configure your API keys for different services.")
    
    # API key status
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("Current Status")
        
        # Check each API key
        api_status = check_api_keys()
        
        for service, status in api_status.items():
            if status["configured"]:
                st.success(f"✅ {service}: Configured")
            else:
                st.error(f"❌ {service}: Not configured")
    
    with col2:
        st.subheader("Quick Actions")
        
        if st.button("🔄 Refresh Status", use_container_width=True):
            st.rerun()
        
        if st.button("📋 Copy .env Template", use_container_width=True):
            copy_env_template()
    
    st.divider()
    
    # API key input forms
    st.subheader("🔧 Configure API Keys")
    st.info("💡 **Tip:** Create a `.env` file in your project root with these keys for persistent configuration.")
    
    with st.form("api_keys_form"):
        # OpenAI API Key
        openai_key = st.text_input(
            "OpenAI API Key",
            value="***" if config.openai_api_key else "",
            type="password",
            help="Required for GPT models. Get your key from https://platform.openai.com/api-keys"
        )
        
        # Anthropic API Key
        anthropic_key = st.text_input(
            "Anthropic API Key",
            value="***" if config.anthropic_api_key else "",
            type="password",
            help="Required for Claude models. Get your key from https://console.anthropic.com/"
        )
        
        # Tavily API Key
        tavily_key = st.text_input(
            "Tavily API Key",
            value="***" if config.tavily_api_key else "",
            type="password",
            help="Required for web search. Get your key from https://tavily.com/"
        )
        
        if st.form_submit_button("💾 Save API Keys", type="primary"):
            save_api_keys(openai_key, anthropic_key, tavily_key)


def display_model_settings():
    """Display model configuration."""
    st.header("🤖 Model Configuration")
    st.markdown("Configure which AI models to use for different tasks.")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Primary LLM Provider")
        
        current_provider = getattr(config, 'default_llm_provider', 'openai')
        provider = st.selectbox(
            "Default Provider",
            ["openai", "anthropic"],
            index=0 if current_provider == "openai" else 1,
            help="Primary AI provider for research tasks"
        )
        
        # Model selection based on provider
        if provider == "openai":
            st.subheader("OpenAI Models")
            openai_model = st.selectbox(
                "GPT Model",
                ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-3.5-turbo"],
                index=0,
                help="OpenAI model for research tasks"
            )
        else:
            st.subheader("Anthropic Models")
            anthropic_model = st.selectbox(
                "Claude Model",
                ["claude-3-5-sonnet-20241022", "claude-3-opus-20240229", "claude-3-haiku-20240307"],
                index=0,
                help="Anthropic model for research tasks"
            )
    
    with col2:
        st.subheader("Model Parameters")
        
        temperature = st.slider(
            "Temperature",
            min_value=0.0,
            max_value=1.0,
            value=0.1,
            step=0.1,
            help="Controls randomness in model responses (0 = deterministic, 1 = creative)"
        )
        
        max_tokens = st.number_input(
            "Max Tokens",
            min_value=100,
            max_value=4000,
            value=2000,
            step=100,
            help="Maximum tokens in model responses"
        )
        
        # Advanced settings
        with st.expander("🔧 Advanced Settings"):
            top_p = st.slider("Top P", 0.0, 1.0, 0.9, 0.1, help="Nucleus sampling parameter")
            frequency_penalty = st.slider("Frequency Penalty", 0.0, 2.0, 0.0, 0.1, help="Penalize repeated tokens")
    
    if st.button("💾 Save Model Settings", type="primary"):
        save_model_settings(provider, locals())


def display_research_settings():
    """Display research configuration."""
    st.header("🔍 Research Configuration")
    st.markdown("Configure how the research workflow operates.")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Search Settings")
        
        max_search_results = st.slider(
            "Max Search Results per Query",
            min_value=5,
            max_value=50,
            value=getattr(config, 'max_search_results', 10),
            help="Maximum number of search results to retrieve per query"
        )
        
        search_depth = st.selectbox(
            "Default Search Depth",
            ["basic", "advanced"],
            index=1,
            help="Default depth for web searches"
        )
        
        max_iterations = st.slider(
            "Max Research Iterations",
            min_value=1,
            max_value=10,
            value=getattr(config, 'max_research_iterations', 3),
            help="Maximum number of research iterations"
        )
    
    with col2:
        st.subheader("Analysis Settings")
        
        enable_deep_analysis = st.checkbox(
            "Enable Deep Analysis by Default",
            value=True,
            help="Perform detailed analysis of research findings"
        )
        
        auto_generate_report = st.checkbox(
            "Auto-Generate Reports",
            value=True,
            help="Automatically generate comprehensive reports"
        )
        
        include_source_analysis = st.checkbox(
            "Include Source Credibility Analysis",
            value=True,
            help="Analyze and score source credibility"
        )
    
    # Workflow settings
    st.subheader("🔄 Workflow Settings")
    
    col3, col4 = st.columns(2)
    
    with col3:
        timeout_settings = st.number_input(
            "Request Timeout (seconds)",
            min_value=30,
            max_value=300,
            value=120,
            help="Timeout for API requests"
        )
        
        retry_attempts = st.number_input(
            "Retry Attempts",
            min_value=1,
            max_value=5,
            value=3,
            help="Number of retry attempts for failed requests"
        )
    
    with col4:
        parallel_processing = st.checkbox(
            "Enable Parallel Processing",
            value=True,
            help="Process multiple queries in parallel when possible"
        )
        
        cache_results = st.checkbox(
            "Cache Search Results",
            value=True,
            help="Cache search results to avoid duplicate queries"
        )
    
    if st.button("💾 Save Research Settings", type="primary"):
        save_research_settings(locals())


def display_interface_settings():
    """Display interface configuration."""
    st.header("🎨 Interface Settings")
    st.markdown("Customize the application interface.")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Display Settings")
        
        theme = st.selectbox(
            "Theme",
            ["Auto", "Light", "Dark"],
            index=0,
            help="Application theme"
        )
        
        results_per_page = st.slider(
            "Results per Page",
            min_value=5,
            max_value=50,
            value=10,
            help="Number of results to display per page"
        )
        
        show_debug_info = st.checkbox(
            "Show Debug Information",
            value=getattr(config, 'debug', False),
            help="Display debug information in the interface"
        )
    
    with col2:
        st.subheader("Notification Settings")
        
        show_progress_notifications = st.checkbox(
            "Show Progress Notifications",
            value=True,
            help="Display notifications during research progress"
        )
        
        auto_refresh_results = st.checkbox(
            "Auto-Refresh Results",
            value=True,
            help="Automatically refresh results during research"
        )
        
        sound_notifications = st.checkbox(
            "Sound Notifications",
            value=False,
            help="Play sound when research completes"
        )
    
    # Export/Import settings
    st.subheader("📁 Data Management")
    
    col3, col4 = st.columns(2)
    
    with col3:
        if st.button("📥 Export Settings", use_container_width=True):
            export_settings()
    
    with col4:
        uploaded_settings = st.file_uploader("📤 Import Settings", type=['json'])
        if uploaded_settings:
            import_settings(uploaded_settings)
    
    if st.button("💾 Save Interface Settings", type="primary"):
        save_interface_settings(locals())


def display_settings_actions():
    """Display global settings actions."""
    st.header("🔧 Settings Actions")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🔄 Reset to Defaults", type="secondary", use_container_width=True):
            reset_to_defaults()
    
    with col2:
        if st.button("✅ Test Configuration", type="secondary", use_container_width=True):
            test_configuration()
    
    with col3:
        if st.button("📋 View Current Config", type="secondary", use_container_width=True):
            display_current_config()
    
    with col4:
        if st.button("💾 Save All Settings", type="primary", use_container_width=True):
            save_all_settings()


def check_api_keys():
    """Check the status of API keys."""
    return {
        "OpenAI": {"configured": bool(config.openai_api_key)},
        "Anthropic": {"configured": bool(config.anthropic_api_key)},
        "Tavily": {"configured": bool(config.tavily_api_key)}
    }


def copy_env_template():
    """Copy .env template to clipboard."""
    template = """# API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
TAVILY_API_KEY=your_tavily_api_key_here

# Model Configuration
DEFAULT_LLM_PROVIDER=openai
DEFAULT_MODEL=gpt-4o
ANTHROPIC_MODEL=claude-3-5-sonnet-20241022

# Application Settings
MAX_SEARCH_RESULTS=10
MAX_RESEARCH_ITERATIONS=3
STREAMLIT_PORT=8501
DEBUG=false"""
    
    st.code(template, language="bash")
    st.success("📋 Template copied! Create a `.env` file with this content.")


def save_api_keys(openai_key, anthropic_key, tavily_key):
    """Save API keys (in a real app, this would update environment variables)."""
    st.success("✅ API keys saved! Restart the application to apply changes.")
    st.info("💡 For persistent storage, add these keys to your `.env` file.")


def save_model_settings(provider, settings):
    """Save model settings."""
    st.success("✅ Model settings saved!")
    st.session_state.settings_changed = True


def save_research_settings(settings):
    """Save research settings."""
    st.success("✅ Research settings saved!")
    st.session_state.settings_changed = True


def save_interface_settings(settings):
    """Save interface settings."""
    st.success("✅ Interface settings saved!")
    st.session_state.settings_changed = True


def reset_to_defaults():
    """Reset all settings to defaults."""
    if st.button("⚠️ Confirm Reset", type="secondary"):
        st.success("✅ Settings reset to defaults!")
        st.session_state.settings_changed = True
        st.rerun()


def test_configuration():
    """Test the current configuration."""
    with st.spinner("Testing configuration..."):
        # Test API keys
        try:
            config.validate_api_keys()
            st.success("✅ Configuration test passed!")
        except ValueError as e:
            st.error(f"❌ Configuration test failed: {str(e)}")


def display_current_config():
    """Display current configuration."""
    st.subheader("📋 Current Configuration")
    
    config_dict = {
        "Default LLM Provider": config.default_llm_provider,
        "Default Model": config.default_model,
        "Max Search Results": config.max_search_results,
        "Max Research Iterations": config.max_research_iterations,
        "Debug Mode": config.debug,
        "API Keys Configured": {
            "OpenAI": bool(config.openai_api_key),
            "Anthropic": bool(config.anthropic_api_key),
            "Tavily": bool(config.tavily_api_key)
        }
    }
    
    st.json(config_dict)


def save_all_settings():
    """Save all settings."""
    st.success("✅ All settings saved!")
    st.session_state.settings_changed = True


def export_settings():
    """Export current settings."""
    st.info("📥 Settings export functionality would be implemented here.")


def import_settings(uploaded_file):
    """Import settings from file."""
    st.info("📤 Settings import functionality would be implemented here.")


if __name__ == "__main__":
    main()
