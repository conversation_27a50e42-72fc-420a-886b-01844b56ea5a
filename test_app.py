"""Simple test script for the multi-agent research application."""

import asyncio
import sys
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

from config import config
from agents.search_agent import SearchAgent
from agents.analysis_agent import AnalysisAgent
from agents.coordinator_agent import CoordinatorAgent
from agents.report_agent import ReportAgent
from agents.base_agent import AgentState
from workflow import WorkflowManager
from langchain_core.messages import HumanMessage


async def test_config():
    """Test configuration loading."""
    print("🔧 Testing Configuration...")
    
    try:
        print(f"✅ Default LLM Provider: {config.default_llm_provider}")
        print(f"✅ Default Model: {config.default_model}")
        print(f"✅ Max Search Results: {config.max_search_results}")
        print(f"✅ Max Research Iterations: {config.max_research_iterations}")
        
        # Test API key validation (without exposing keys)
        has_openai = bool(config.openai_api_key)
        has_anthropic = bool(config.anthropic_api_key)
        has_tavily = bool(config.tavily_api_key)
        
        print(f"✅ OpenAI API Key: {'Configured' if has_openai else 'Missing'}")
        print(f"✅ Anthropic API Key: {'Configured' if has_anthropic else 'Missing'}")
        print(f"✅ Tavily API Key: {'Configured' if has_tavily else 'Missing'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {str(e)}")
        return False


async def test_agents():
    """Test individual agent initialization."""
    print("\n🤖 Testing Agent Initialization...")
    
    try:
        # Test agent creation
        search_agent = SearchAgent()
        analysis_agent = AnalysisAgent()
        coordinator_agent = CoordinatorAgent()
        report_agent = ReportAgent()
        
        print(f"✅ Search Agent: {search_agent.name}")
        print(f"✅ Analysis Agent: {analysis_agent.name}")
        print(f"✅ Coordinator Agent: {coordinator_agent.name}")
        print(f"✅ Report Agent: {report_agent.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent initialization failed: {str(e)}")
        return False


async def test_workflow_manager():
    """Test workflow manager initialization."""
    print("\n🔄 Testing Workflow Manager...")
    
    try:
        workflow_manager = WorkflowManager()
        
        # Test status retrieval
        status = workflow_manager.get_status()
        print(f"✅ Workflow Status: {status}")
        
        # Test workflow state
        print(f"✅ Is Running: {workflow_manager.is_workflow_running()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Workflow manager test failed: {str(e)}")
        return False


async def test_mock_research():
    """Test a mock research workflow (without API calls)."""
    print("\n🔍 Testing Mock Research Workflow...")
    
    try:
        # Create a simple test state
        test_state = AgentState(
            messages=[HumanMessage(content="Test research task", name="User")],
            current_task="Test research on artificial intelligence",
            context={},
            metadata={}
        )
        
        # Test coordinator decision making (mock)
        coordinator = CoordinatorAgent()
        coordinator.workflow_state = "initialized"
        coordinator.iteration_count = 0
        
        # Mock process (without actual LLM calls)
        print("✅ Created test state")
        print("✅ Initialized coordinator")
        print("✅ Mock workflow structure validated")
        
        return True
        
    except Exception as e:
        print(f"❌ Mock research test failed: {str(e)}")
        return False


async def test_streamlit_imports():
    """Test Streamlit page imports."""
    print("\n📱 Testing Streamlit Page Imports...")
    
    try:
        # Test if pages can be imported
        import pages
        print("✅ Pages module imported")
        
        # Test individual page files exist
        page_files = [
            "pages/1_🔍_Research.py",
            "pages/2_📚_History.py", 
            "pages/3_⚙️_Settings.py"
        ]
        
        for page_file in page_files:
            if Path(page_file).exists():
                print(f"✅ {page_file} exists")
            else:
                print(f"❌ {page_file} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Streamlit imports test failed: {str(e)}")
        return False


async def run_all_tests():
    """Run all tests."""
    print("🧪 Starting Application Tests...\n")
    
    tests = [
        ("Configuration", test_config),
        ("Agent Initialization", test_agents),
        ("Workflow Manager", test_workflow_manager),
        ("Mock Research", test_mock_research),
        ("Streamlit Imports", test_streamlit_imports)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 Test Summary:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"Total: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Application is ready to use.")
        return True
    else:
        print("⚠️  Some tests failed. Check configuration and dependencies.")
        return False


def print_usage_instructions():
    """Print usage instructions."""
    print("\n📖 Usage Instructions:")
    print("=" * 50)
    print("1. Ensure all API keys are configured in .env file")
    print("2. Run the application: streamlit run main.py")
    print("3. Navigate to the Research page to start researching")
    print("4. View results in the History page")
    print("5. Configure settings in the Settings page")
    print("\n🔑 Required API Keys:")
    print("- TAVILY_API_KEY (required for web search)")
    print("- OPENAI_API_KEY or ANTHROPIC_API_KEY (required for LLM)")
    print("\n🌐 Get API Keys:")
    print("- Tavily: https://tavily.com/")
    print("- OpenAI: https://platform.openai.com/api-keys")
    print("- Anthropic: https://console.anthropic.com/")


async def main():
    """Main test function."""
    success = await run_all_tests()
    
    if success:
        print_usage_instructions()
    
    return success


if __name__ == "__main__":
    # Run tests
    success = asyncio.run(main())
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)
